defmodule Drops.Relation.Schema.DatabaseIntrospector do
  @moduledoc """
  Database introspection utilities for extracting schema metadata.

  This module provides functions to introspect database-level information
  that is not available through Ecto schemas, such as index information.

  Currently supports SQLite with plans to add PostgreSQL support.
  """

  alias Drops.Relation.Schema.{Index, Indices}

  @doc """
  Extracts index information for a table from the database.

  ## Parameters

  - `repo` - The Ecto repository module
  - `table_name` - The name of the table to introspect

  ## Returns

  Returns `{:ok, %Indices{}}` on success or `{:error, reason}` on failure.

  ## Examples

      iex> Drops.Relation.Schema.DatabaseIntrospector.get_table_indices(MyRepo, "users")
      {:ok, %Drops.Relation.Schema.Indices{indices: [...]}}
  """
  @spec get_table_indices(module(), String.t()) :: {:ok, Indices.t()} | {:error, term()}
  def get_table_indices(repo, table_name) when is_binary(table_name) do
    # Detect database adapter and use appropriate introspection method
    case get_adapter_type(repo) do
      :sqlite ->
        get_sqlite_indices(repo, table_name)

      :postgres ->
        get_postgres_indices(repo, table_name)

      adapter ->
        {:error, {:unsupported_adapter, adapter}}
    end
  end

  @doc """
  Extracts index information for SQLite databases.

  Uses SQLite PRAGMA statements to introspect index information.
  """
  @spec get_sqlite_indices(module(), String.t()) :: {:ok, Indices.t()} | {:error, term()}
  def get_sqlite_indices(repo, table_name) do
    # SQLite PRAGMA to get index list
    index_list_query = "PRAGMA index_list(#{table_name})"

    case repo.query(index_list_query) do
      {:ok, %{rows: rows}} ->
        # PRAGMA index_list returns: [seq, name, unique, origin, partial]
        indices =
          for [_seq, name, unique, _origin, _partial] <- rows do
            # Get index details
            index_info_query = "PRAGMA index_info(#{name})"

            case repo.query(index_info_query) do
              {:ok, %{rows: info_rows}} ->
                # PRAGMA index_info returns: [seqno, cid, name]
                field_names =
                  info_rows
                  # Sort by seqno
                  |> Enum.sort_by(&hd/1)
                  |> Enum.map(fn [_seqno, _cid, field_name] ->
                    String.to_atom(field_name)
                  end)

                Index.from_names(name, field_names, unique == 1, :btree)

              {:error, _} ->
                # If we can't get field info, create index with empty fields
                Index.from_names(name, [], unique == 1, :btree)
            end
          end
          |> Enum.reject(&is_nil/1)

        {:ok, Indices.new(indices)}

      {:error, error} ->
        {:error, error}
    end
  end

  @doc """
  Extracts index information for PostgreSQL databases.

  Uses PostgreSQL system catalogs to introspect index information.
  """
  @spec get_postgres_indices(module(), String.t()) ::
          {:ok, Indices.t()} | {:error, term()}
  def get_postgres_indices(repo, table_name) do
    query = """
    SELECT
        i.relname as index_name,
        array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
        ix.indisunique as is_unique,
        am.amname as index_type
    FROM pg_class t
    JOIN pg_index ix ON t.oid = ix.indrelid
    JOIN pg_class i ON i.oid = ix.indexrelid
    JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
    JOIN pg_am am ON i.relam = am.oid
    WHERE t.relname = $1
      AND NOT ix.indisprimary  -- Exclude primary key indices
    GROUP BY i.relname, ix.indisunique, am.amname
    ORDER BY i.relname
    """

    case repo.query(query, [table_name]) do
      {:ok, %{rows: rows}} ->
        indices =
          for [index_name, column_names, is_unique, index_type] <- rows do
            field_names = Enum.map(column_names, &String.to_atom/1)
            type = postgres_index_type_to_atom(index_type)

            Index.from_names(index_name, field_names, is_unique, type)
          end

        {:ok, Indices.new(indices)}

      {:error, error} ->
        {:error, error}
    end
  end

  # Private helper functions

  defp get_adapter_type(repo) do
    case repo.__adapter__() do
      Ecto.Adapters.SQLite3 -> :sqlite
      Ecto.Adapters.Postgres -> :postgres
      _ -> :unknown
    end
  end

  defp postgres_index_type_to_atom(type_string) do
    case type_string do
      "btree" -> :btree
      "hash" -> :hash
      "gin" -> :gin
      "gist" -> :gist
      "brin" -> :brin
      _ -> nil
    end
  end
end
